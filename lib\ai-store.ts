import { create } from 'zustand';
import { Model<PERSON>rovider, ModelConfig } from './models';
import { getAllModels, getModelsByProvider } from './models';

// 自定义模型接口
export interface CustomModel {
  id: string;
  name: string;
  provider: ModelProvider;
  maxTokens?: number;
  temperature?: number;
}

// AI状态接口
interface AIState {
  provider: ModelProvider;
  model: string;
  customModels: CustomModel[];
  apiKeys: Record<ModelProvider, string>;
  baseUrls: Record<ModelProvider, string>;
  maxTokens?: number;
  temperature?: number;
  isInitialized: boolean;
  setProvider: (provider: ModelProvider) => void;
  setModel: (model: string) => void;
  setCustomModels: (models: CustomModel[]) => void;
  setApiKey: (provider: ModelProvider, apiKey: string) => void;
  setBaseUrl: (provider: ModelProvider, baseUrl: string) => void;
  setMaxTokens: (maxTokens?: number) => void;
  setTemperature: (temperature?: number) => void;
  reset: () => void;
  resetInitialization: () => void;
  initializeFromCookies: (cookies: any) => Promise<void>;
}

// 默认状态
const DEFAULT_STATE = {
  provider: 'openai' as ModelProvider,
  model: 'gpt-4o',
  customModels: [] as CustomModel[],
  apiKeys: {
    openai: '',
    xai: '',
    deepseek: '',
    anthropic: ''
  },
  baseUrls: {
    openai: '',
    xai: '',
    deepseek: '',
    anthropic: ''
  },
  isInitialized: false
};

/**
 * AI状态管理存储
 * 使用Zustand管理AI提供商和模型的状态
 */
export const useAIStore = create<AIState>((set, get) => ({
  // 初始状态
  provider: DEFAULT_STATE.provider,
  model: DEFAULT_STATE.model,
  customModels: DEFAULT_STATE.customModels,
  apiKeys: DEFAULT_STATE.apiKeys,
  baseUrls: DEFAULT_STATE.baseUrls,
  maxTokens: undefined,
  temperature: undefined,
  isInitialized: DEFAULT_STATE.isInitialized,

  // 设置提供商
  setProvider: (provider: ModelProvider) => set((state) => {
    // 获取新提供商的模型列表
    const defaultModels = getModelsByProvider(provider);

    // 检查当前模型是否在新提供商的模型列表中
    const modelExists = defaultModels.some(m => m.id === state.model) ||
                       state.customModels.some(m => m.id === state.model && m.provider === provider);

    // 如果当前模型不在新提供商的模型列表中，选择第一个可用模型
    const newModel = modelExists ? state.model : (defaultModels[0]?.id || '');

    return {
      provider,
      model: newModel,
    };
  }),

  // 设置模型
  setModel: (model: string) => set({ model }),

  // 设置自定义模型
  setCustomModels: (customModels: CustomModel[]) => set({ customModels }),

  // 设置API密钥
  setApiKey: (provider: ModelProvider, apiKey: string) => set((state) => ({
    apiKeys: {
      ...state.apiKeys,
      [provider]: apiKey
    }
  })),

  // 设置基础URL
  setBaseUrl: (provider: ModelProvider, baseUrl: string) => set((state) => ({
    baseUrls: {
      ...state.baseUrls,
      [provider]: baseUrl
    }
  })),

  // 设置最大Token数
  setMaxTokens: (maxTokens?: number) => set({ maxTokens }),

  // 设置温度
  setTemperature: (temperature?: number) => set({ temperature }),

  // 重置状态
  reset: () => set(DEFAULT_STATE),

  // 重置初始化状态
  resetInitialization: () => set({ isInitialized: false }),

  // 从Cookie初始化配置
  initializeFromCookies: async (cookies: any) => {
    try {
      const currentState = get();

      // 只在未初始化时才从Cookie加载配置
      if (currentState.isInitialized) {
        return;
      }

      const configManager = new AIConfigManager(cookies);
      const config = await configManager.getConfig();

      set({
        provider: config.provider,
        model: config.model,
        customModels: config.customModels,
        apiKeys: config.apiKeys,
        baseUrls: config.baseUrls,
        maxTokens: config.maxTokens,
        temperature: config.temperature,
        isInitialized: true,
      });
    } catch (error) {
      console.error('初始化AI配置失败:', error);
    }
  },
}));

/**
 * 获取当前提供商的所有模型列表（包括自定义模型）
 */
export function getAllModelsForProvider(provider: ModelProvider, customModels: CustomModel[] = []): (ModelConfig | CustomModel)[] {
  const defaultModels = getModelsByProvider(provider);
  const providerCustomModels = customModels.filter(m => m.provider === provider);
  return [...defaultModels, ...providerCustomModels];
}

/**
 * AI配置管理器
 * 用于保存和加载AI配置
 */
export class AIConfigManager {
  private cookies: any;

  constructor(cookies: any) {
    this.cookies = cookies;
  }

  /**
   * 获取配置
   */
  async getConfig() {
    // 检查是否为服务端环境（Next.js 15需要await cookies）
    const isServerSide = typeof window === 'undefined';

    let cookieStore = this.cookies;
    if (isServerSide && typeof this.cookies === 'function') {
      cookieStore = await this.cookies();
    }

    const config = {
      provider: cookieStore.get('ai_provider')?.value || DEFAULT_STATE.provider,
      model: cookieStore.get('ai_model')?.value || DEFAULT_STATE.model,
      apiKeys: {
        openai: cookieStore.get('ai_api_key_openai')?.value || '',
        xai: cookieStore.get('ai_api_key_xai')?.value || '',
        deepseek: cookieStore.get('ai_api_key_deepseek')?.value || '',
        anthropic: cookieStore.get('ai_api_key_anthropic')?.value || ''
      },
      baseUrls: {
        openai: cookieStore.get('ai_base_url_openai')?.value || '',
        xai: cookieStore.get('ai_base_url_xai')?.value || '',
        deepseek: cookieStore.get('ai_base_url_deepseek')?.value || '',
        anthropic: cookieStore.get('ai_base_url_anthropic')?.value || ''
      },
      customModels: [],
      maxTokens: undefined as number | undefined,
      temperature: undefined as number | undefined
    };

    // 获取自定义模型
    try {
      const customModelsJson = cookieStore.get('ai_custom_models')?.value;
      if (customModelsJson) {
        config.customModels = JSON.parse(customModelsJson);
      }
    } catch (error) {
      console.error('解析自定义模型失败:', error);
    }

    // 获取全局设置
    try {
      const maxTokensStr = cookieStore.get('ai_max_tokens')?.value;
      if (maxTokensStr && maxTokensStr !== '') {
        const maxTokens = parseInt(maxTokensStr);
        if (!isNaN(maxTokens) && maxTokens > 0) {
          config.maxTokens = maxTokens;
        }
      }

      const temperatureStr = cookieStore.get('ai_temperature')?.value;
      if (temperatureStr && temperatureStr !== '') {
        const temperature = parseFloat(temperatureStr);
        if (!isNaN(temperature) && temperature >= 0 && temperature <= 2) {
          config.temperature = temperature;
        }
      }
    } catch (error) {
      console.error('解析全局设置失败:', error);
    }

    return config;
  }

  /**
   * 保存配置
   */
  async saveConfig(config: {
    provider: ModelProvider;
    model: string;
    apiKeys: Record<ModelProvider, string>;
    baseUrls: Record<ModelProvider, string>;
    customModels: CustomModel[];
    maxTokens?: number;
    temperature?: number;
  }) {
    // 保存基本配置
    this.cookies.set('ai_provider', config.provider);
    this.cookies.set('ai_model', config.model);

    // 保存API密钥
    Object.entries(config.apiKeys).forEach(([provider, apiKey]) => {
      this.cookies.set(`ai_api_key_${provider}`, apiKey);
    });

    // 保存基础URL
    Object.entries(config.baseUrls).forEach(([provider, baseUrl]) => {
      this.cookies.set(`ai_base_url_${provider}`, baseUrl);
    });

    // 保存自定义模型
    if (config.customModels && config.customModels.length > 0) {
      this.cookies.set('ai_custom_models', JSON.stringify(config.customModels));
    } else {
      this.cookies.set('ai_custom_models', '');
    }

    // 保存全局设置
    if (config.maxTokens !== undefined) {
      this.cookies.set('ai_max_tokens', config.maxTokens.toString());
    }

    if (config.temperature !== undefined) {
      this.cookies.set('ai_temperature', config.temperature.toString());
    }
  }
}
