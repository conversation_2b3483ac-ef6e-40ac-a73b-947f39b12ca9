import { NextRequest, NextResponse } from 'next/server';
import { createAIClient, Message } from '@/lib/ai-client';
import { ModelProvider, getModelById } from '@/lib/models';
import { createDefaultPrompt, createPresentationPrompt, createDataVisualizationPrompt } from '@/lib/prompt-modules';
import { AIConfigManager } from '@/lib/ai-store';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { messages, model } = body;
    // 原始消息，可能包含 'task' 角色
    const rawMessages = messages as Array<{ role: string; content: string }>;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Invalid messages format' },
        { status: 400 }
      );
    }

    // 在没有 system 消息时，前置系统提示
    if (!rawMessages.some(msg => msg.role === 'system')) {
      let systemPrompt = createPresentationPrompt();
      console.log('使用系统提示:', systemPrompt);

      rawMessages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    // 为API准备消息：将自定义 'task' 角色映射为 'user'
    const apiMessages: Message[] = rawMessages.map(msg => ({
      role: msg.role === 'task' ? 'user' : (msg.role as 'user' | 'assistant' | 'system'),
      content: msg.content,
    }));

    // 过滤掉空内容消息，避免 API 报错
    const filteredMessages = apiMessages.filter(m => m.content?.trim());

    // 获取API密钥和基础URL
    const providerCookie = request.cookies.get('ai_provider');
    const modelCookie = request.cookies.get('ai_model');

    // 获取模型配置
    const cookieStore = await cookies();
    const configManager = new AIConfigManager(cookieStore);
    const { customModels } = await configManager.getConfig();
    const modelConfig = getModelById(model, customModels);

    // 根据模型配置或模型名称确定提供商
    let provider: ModelProvider;

    console.log('模型名称:', model);
    console.log('找到的模型配置:', modelConfig);

    if (modelConfig && 'provider' in modelConfig) {
      // 如果找到模型配置，使用配置中的提供商
      provider = modelConfig.provider as ModelProvider;
      console.log('使用模型配置中的提供商:', provider);
    } else if (model.startsWith('gpt-') || model === 'gpt-4o') {
      provider = 'openai';
    } else if (model.startsWith('grok-')) {
      provider = 'xai';
    } else if (model === 'deepseek-coder' || model.startsWith('deepseek-')) {
      provider = 'deepseek';
    } else if (model.startsWith('claude-')) {
      provider = 'anthropic';
    } else {
      provider = 'openai';
    }

    console.log('识别的提供商:', provider);

    // 获取特定提供商的API密钥和基础URL
    const apiKeyCookie = request.cookies.get(`ai_api_key_${provider}`);
    const baseUrlCookie = request.cookies.get(`ai_base_url_${provider}`);
    const apiKey = apiKeyCookie?.value || '';
    const baseUrl = baseUrlCookie?.value || '';

    console.log('API调用配置:', {
      model,
      provider,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      hasBaseUrl: !!baseUrl,
      baseUrlLength: baseUrl?.length || 0,
      cookieName: `ai_api_key_${provider}`
    });

    // 创建API密钥和基础URL对象
    const apiKeys: Partial<Record<ModelProvider, string>> = {};
    apiKeys[provider as ModelProvider] = apiKey;

    const baseUrls: Partial<Record<ModelProvider, string>> = {};
    if (baseUrl) {
      baseUrls[provider as ModelProvider] = baseUrl;
    }

    // 创建AI客户端
    const aiClient = createAIClient({
      apiKeys: apiKeys as Record<ModelProvider, string>,
      baseUrls: baseUrls as Record<ModelProvider, string>,
      customModels: customModels
    });

    // 调用流式聊天完成
    const encoder = new TextEncoder();
    let fullContent = '';
    let isIncomplete = false;

    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          await aiClient.streamingChatCompletion(
            {
              model,
              messages: filteredMessages,
            },
            // onContent callback
            (content: string) => {
              fullContent += content;
              controller.enqueue(encoder.encode(content));
            },
            // onError callback
            (error: Error) => {
              console.error('流式聊天错误:', error);
              controller.error(error);
            },
            // onFinish callback
            async (response) => {
              console.log('流式聊天完成:', response);

              // 检测是否因为token限制而截断
              const isTokenLimitReached = response.usage &&
                response.usage.completionTokens >= (modelConfig?.maxTokens ?? 4096) * 0.95;

              // 检测内容是否不完整（简单启发式检测）
              const hasIncompleteMarkers = fullContent.includes('```') &&
                !fullContent.match(/```[\s\S]*?```$/);

              isIncomplete = isTokenLimitReached || hasIncompleteMarkers;

              if (isIncomplete) {
                console.log('检测到内容不完整，准备续写...');

                // 发送续写标记
                controller.enqueue(encoder.encode('\n\n[继续生成中...]'));

                // 自动续写
                try {
                  // 检测最后的内容片段，用于续写提示
                  const lastLines = fullContent.split('\n').slice(-5).join('\n');
                  const continuePrompt = `请继续完成上述内容，从中断的地方继续。重要要求：
1. 不要重复已有内容
2. 不要添加新的代码块标记
3. 直接从中断处继续输出原始内容
4. 保持与前面内容的连贯性
5. 最后几行内容是：
${lastLines}

请从这里继续，不要重复这些内容。`;

                  const continueMessages = [
                    ...filteredMessages,
                    { role: 'assistant' as const, content: fullContent },
                    { role: 'user' as const, content: continuePrompt }
                  ];

                  await aiClient.streamingChatCompletion(
                    {
                      model,
                      messages: continueMessages,
                    },
                    // 续写内容回调
                    (content: string) => {
                      // 清理续写内容，移除可能的重复部分
                      let cleanContent = content;

                      // 移除续写标记
                      cleanContent = cleanContent.replace(/^\s*\[继续生成中\.\.\.\]\s*/g, '');

                      // 更智能的重复内容检测和移除
                      const lastLines = fullContent.split('\n').slice(-10); // 取最后10行
                      const continueLines = cleanContent.split('\n');

                      // 找到第一个不重复的行
                      let startIndex = 0;
                      for (let i = 0; i < continueLines.length; i++) {
                        const line = continueLines[i].trim();
                        if (line && !lastLines.some(lastLine => lastLine.trim() === line)) {
                          startIndex = i;
                          break;
                        }
                      }

                      // 从第一个不重复的行开始
                      if (startIndex > 0) {
                        cleanContent = continueLines.slice(startIndex).join('\n');
                      }

                      // 移除各种可能的代码块开始标记
                      cleanContent = cleanContent.replace(/^```html\{filename=[^}]+\}\s*/g, '');
                      cleanContent = cleanContent.replace(/^```html\s*/g, '');
                      cleanContent = cleanContent.replace(/^```markdown\{filename=[^}]+\}\s*/g, '');
                      cleanContent = cleanContent.replace(/^```markdown\s*/g, '');
                      cleanContent = cleanContent.replace(/^```md\s*/g, '');
                      cleanContent = cleanContent.replace(/^```\s*/g, '');

                      // 移除结尾的代码块标记
                      cleanContent = cleanContent.replace(/\s*```\s*$/g, '');

                      // 移除中间可能出现的代码块标记
                      cleanContent = cleanContent.replace(/\n```html\{filename=[^}]+\}\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```html\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```markdown\{filename=[^}]+\}\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```markdown\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```md\s*/g, '\n');

                      if (cleanContent.trim()) {
                        fullContent += cleanContent; // 重要：将清理后的续写内容添加到完整内容中
                        controller.enqueue(encoder.encode(cleanContent));
                      }
                    },
                    // 续写错误回调
                    (error: Error) => {
                      console.error('续写错误:', error);
                      controller.enqueue(encoder.encode('\n\n[续写失败，请手动重试]'));
                      controller.close();
                    },
                    // 续写完成回调
                    (continueResponse) => {
                      console.log('续写完成:', continueResponse);
                      controller.close();
                    }
                  );
                } catch (continueError) {
                  console.error('续写启动错误:', continueError);
                  controller.enqueue(encoder.encode('\n\n[续写失败，请手动重试]'));
                  controller.close();
                }
              } else {
                controller.close();
              }
            }
          );
        } catch (error) {
          console.error('流式聊天启动错误:', error);
          controller.error(error);
        }
      },
      cancel() {
        console.log('流式聊天被取消');
      }
    });

    return new Response(readableStream, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
  } catch (error) {
    console.error('Error in chat-stream API:', error);

    return NextResponse.json({
      error: '流式聊天服务暂时不可用，请稍后再试或检查您的API配置。',
    }, { status: 500 });
  }
}
