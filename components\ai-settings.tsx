'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import { useCookies } from 'next-client-cookies';
import { AIConfigManager, useAIStore, CustomModel } from '@/lib/ai-store';
import { ModelProvider } from '@/lib/models';
import { getAllModels, getModelsByProvider } from '@/lib/models';
import * as Dialog from '@radix-ui/react-dialog';
import * as Select from '@radix-ui/react-select';
import { CheckIcon, ChevronDownIcon, Cross2Icon, PlusIcon, UploadIcon, DownloadIcon, TrashIcon } from '@radix-ui/react-icons';

// 提供商列表
const PROVIDERS: { label: string; value: ModelProvider }[] = [
  { label: 'OpenAI', value: 'openai' },
  { label: 'xAI (Grok)', value: 'xai' },
  { label: 'DeepSeek', value: 'deepseek' },
  { label: 'Anthropic', value: 'anthropic' },
];

export default function AISettings() {
  const cookies = useCookies();
  const configManager = useMemo(() => new AIConfigManager(cookies), [cookies]);
  const {
    provider,
    model,
    customModels,
    apiKeys,
    baseUrls,
    maxTokens,
    temperature,
    setProvider,
    setModel,
    setCustomModels,
    setApiKey,
    setBaseUrl,
    setMaxTokens,
    setTemperature,
    resetInitialization
  } = useAIStore();

  // 本地状态
  const [isOpen, setIsOpen] = useState(false);
  const [currentApiKey, setCurrentApiKey] = useState('');
  const [currentBaseUrl, setCurrentBaseUrl] = useState('');
  const [currentMaxTokens, setCurrentMaxTokens] = useState<number | undefined>(maxTokens);
  const [currentTemperature, setCurrentTemperature] = useState<number | undefined>(temperature);
  const [isSaved, setIsSaved] = useState(false);
  const [showAddModel, setShowAddModel] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [modelToDelete, setModelToDelete] = useState<CustomModel | null>(null);
  const [newModel, setNewModel] = useState<CustomModel>({
    id: '',
    name: '',
    provider: provider,
    maxTokens: 4096,
    temperature: 0.7,
  });
  const [localModels, setLocalModels] = useState<CustomModel[]>(customModels);
  const [isInitialized, setIsInitialized] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取当前提供商的模型列表
  const allModels = getModelsByProvider(provider, localModels);

  // 加载配置
  useEffect(() => {
    if (isOpen) {
      const loadConfig = async () => {
        const config = await configManager.getConfig();
        setCurrentApiKey(config.apiKeys[provider] || '');
        setCurrentBaseUrl(config.baseUrls[provider] || '');
        setCurrentMaxTokens(config.maxTokens);
        setCurrentTemperature(config.temperature);

        // 只在首次初始化时从Cookie加载自定义模型
        if (!isInitialized) {
          setLocalModels(config.customModels || []);
          setIsInitialized(true);
        }
      };
      loadConfig();
    }
  }, [isOpen, provider, configManager, isInitialized]);

  // 保存配置
  const handleSave = async () => {
    // 更新API密钥和基础URL
    setApiKey(provider, currentApiKey);
    setBaseUrl(provider, currentBaseUrl);

    // 更新全局设置
    setMaxTokens(currentMaxTokens);
    setTemperature(currentTemperature);

    // 更新自定义模型
    setCustomModels(localModels);

    // 保存到cookies
    await configManager.saveConfig({
      provider,
      model,
      apiKeys: {
        ...apiKeys,
        [provider]: currentApiKey
      },
      baseUrls: {
        ...baseUrls,
        [provider]: currentBaseUrl
      },
      customModels: localModels,
      maxTokens: currentMaxTokens,
      temperature: currentTemperature,
    });

    // 显示保存成功提示
    setIsSaved(true);
    setTimeout(() => setIsSaved(false), 2000);

    // 重置初始化状态，以便下次打开时能正确加载已保存的配置
    setIsInitialized(false);
    resetInitialization();
  };

  // 处理提供商变更
  const handleProviderChange = async (value: string) => {
    const newProvider = value as ModelProvider;
    setProvider(newProvider);

    // 加载该提供商的配置
    const config = await configManager.getConfig();
    setCurrentApiKey(config.apiKeys[newProvider] || '');
    setCurrentBaseUrl(config.baseUrls[newProvider] || '');

    // 重新加载自定义模型（提供商变更时需要重新加载）
    setLocalModels(config.customModels || []);

    // 更新新模型的提供商
    setNewModel({...newModel, provider: newProvider});
  };

  // 处理添加自定义模型
  const handleAddModel = () => {
    if (newModel.name && newModel.id) {
      const updatedModels = [...localModels, newModel];
      setLocalModels(updatedModels);
      setNewModel({
        id: '',
        name: '',
        provider,
        maxTokens: 4096,
        temperature: 0.7,
      });
      setShowAddModel(false);

      // 自动选择新添加的模型
      setModel(newModel.id);
    }
  };

  // 处理删除自定义模型
  const handleDeleteModel = (modelToDelete: CustomModel) => {
    setModelToDelete(modelToDelete);
    setShowDeleteConfirm(true);
  };

  // 确认删除模型
  const confirmDeleteModel = async () => {
    if (modelToDelete) {
      const updatedModels = localModels.filter(m => m.id !== modelToDelete.id);
      setLocalModels(updatedModels);

      // 如果删除的是当前选中的模型，切换到默认模型
      if (model === modelToDelete.id) {
        const availableModels = getModelsByProvider(provider, updatedModels);
        setModel(availableModels[0]?.id || '');
      }
    }
    setShowDeleteConfirm(false);
    setModelToDelete(null);
  };

  // 处理配置文件导入
  const handleImportConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const config = JSON.parse(e.target?.result as string);
          if (config.customModels) {
            setLocalModels(config.customModels);
          }
          // 清除文件选择
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
        } catch (error) {
          console.error('配置文件解析失败:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  // 处理配置文件导出
  const handleExportConfig = () => {
    const config = {
      customModels: localModels,
    };
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ai-config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
      <Dialog.Trigger asChild>
        <button className="rounded-md bg-gray-100 px-3 py-2 text-sm text-gray-600 hover:bg-gray-200">
          AI设置
        </button>
      </Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/20" />
        <Dialog.Content className="fixed left-1/2 top-1/2 w-full max-w-md -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white p-6 shadow-xl">
          <Dialog.Title className="text-lg font-medium text-gray-900">
            AI设置
          </Dialog.Title>
          <Dialog.Description className="mt-2 text-sm text-gray-500">
            配置AI提供商、模型和API密钥
          </Dialog.Description>

          <div className="mt-6 space-y-4">
            {/* 提供商选择 */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                提供商
              </label>
              <Select.Root value={provider} onValueChange={handleProviderChange}>
                <Select.Trigger className="inline-flex w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500">
                  <Select.Value />
                  <Select.Icon>
                    <ChevronDownIcon />
                  </Select.Icon>
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="overflow-hidden rounded-md bg-white shadow-lg">
                    <Select.Viewport className="p-1">
                      {PROVIDERS.map((p) => (
                        <Select.Item
                          key={p.value}
                          value={p.value}
                          className="relative flex cursor-default select-none items-center rounded-md px-8 py-2 text-sm text-gray-900 data-[highlighted]:bg-blue-50 data-[highlighted]:text-blue-600"
                        >
                          <Select.ItemText>{p.label}</Select.ItemText>
                          <Select.ItemIndicator className="absolute left-2 inline-flex items-center">
                            <CheckIcon />
                          </Select.ItemIndicator>
                        </Select.Item>
                      ))}
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </div>

            {/* 模型选择 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  模型
                </label>
                <button
                  onClick={() => setShowAddModel(true)}
                  className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                >
                  <PlusIcon className="mr-1 h-4 w-4" />
                  添加自定义模型
                </button>
              </div>
              <Select.Root value={model} onValueChange={setModel}>
                <Select.Trigger className="inline-flex w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500">
                  <Select.Value />
                  <Select.Icon>
                    <ChevronDownIcon />
                  </Select.Icon>
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="overflow-hidden rounded-md bg-white shadow-lg">
                    <Select.Viewport className="p-1">
                      {allModels.map((m, index) => (
                        <Select.Item
                          key={`${m.id}-${index}`}
                          value={m.id}
                          className="relative flex cursor-default select-none items-center rounded-md px-8 py-2 text-sm text-gray-900 data-[highlighted]:bg-blue-50 data-[highlighted]:text-blue-600"
                        >
                          <Select.ItemText>{m.name || m.id}</Select.ItemText>
                          <Select.ItemIndicator className="absolute left-2 inline-flex items-center">
                            <CheckIcon />
                          </Select.ItemIndicator>
                          {localModels.some(cm => cm.id === m.id) && (
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleDeleteModel(m as CustomModel);
                              }}
                              className="absolute right-2 text-gray-400 hover:text-gray-600"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </Select.Item>
                      ))}
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </div>

            {/* 自定义模型输入 */}
            {showAddModel && (
              <div className="space-y-2 rounded-md border border-gray-200 p-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    模型名称
                  </label>
                  <input
                    type="text"
                    value={newModel.name}
                    onChange={(e) => setNewModel({ ...newModel, name: e.target.value })}
                    placeholder="输入模型显示名称"
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    模型ID
                  </label>
                  <input
                    type="text"
                    value={newModel.id}
                    onChange={(e) => setNewModel({ ...newModel, id: e.target.value })}
                    placeholder="输入模型ID"
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    最大Token数 (可选)
                  </label>
                  <input
                    type="number"
                    value={newModel.maxTokens || ''}
                    onChange={(e) => setNewModel({ ...newModel, maxTokens: parseInt(e.target.value) || undefined })}
                    placeholder="输入最大Token数"
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    温度 (可选)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    max="2"
                    value={newModel.temperature || ''}
                    onChange={(e) => setNewModel({ ...newModel, temperature: parseFloat(e.target.value) || undefined })}
                    placeholder="输入温度值 (0-2)"
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
                <div className="mt-4 flex justify-end space-x-2">
                  <button
                    onClick={() => setShowAddModel(false)}
                    className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleAddModel}
                    className="rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
                  >
                    添加
                  </button>
                </div>
              </div>
            )}

            {/* 删除确认对话框 */}
            {showDeleteConfirm && (
              <div className="fixed inset-0 z-50 flex items-center justify-center">
                <div className="fixed inset-0 bg-black/50" onClick={() => setShowDeleteConfirm(false)} />
                <div className="relative rounded-lg bg-white p-6 shadow-xl">
                  <h3 className="text-lg font-medium text-gray-900">确认删除</h3>
                  <p className="mt-2 text-sm text-gray-500">
                    确定要删除模型 "{modelToDelete?.name || modelToDelete?.id}" 吗？此操作无法撤销。
                  </p>
                  <div className="mt-4 flex justify-end space-x-2">
                    <button
                      onClick={() => {
                        setShowDeleteConfirm(false);
                        setModelToDelete(null);
                      }}
                      className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                    >
                      取消
                    </button>
                    <button
                      onClick={confirmDeleteModel}
                      className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* API密钥输入 */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                API密钥
              </label>
              <input
                type="password"
                value={currentApiKey}
                onChange={(e) => setCurrentApiKey(e.target.value)}
                placeholder={`输入${PROVIDERS.find(p => p.value === provider)?.label || provider} API密钥`}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            {/* 基础URL输入 */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                基础URL (可选)
              </label>
              <input
                type="text"
                value={currentBaseUrl}
                onChange={(e) => setCurrentBaseUrl(e.target.value)}
                placeholder={`自定义${PROVIDERS.find(p => p.value === provider)?.label || provider} API地址`}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>

            {/* 全局设置 */}
            <div className="space-y-4 border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-900">全局设置</h4>

              {/* 最大Token数 */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  最大Token数 (可选)
                </label>
                <input
                  type="number"
                  value={currentMaxTokens || ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setCurrentMaxTokens(value ? parseInt(value) : undefined);
                  }}
                  placeholder="如：4096, 8192, 32000"
                  min="1"
                  max="200000"
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500">
                  留空使用模型默认值。不同模型支持的最大Token数不同。
                </p>
              </div>

              {/* 温度 */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  温度 (可选)
                </label>
                <input
                  type="number"
                  value={currentTemperature || ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setCurrentTemperature(value ? parseFloat(value) : undefined);
                  }}
                  placeholder="0.0 - 2.0，如：0.7"
                  min="0"
                  max="2"
                  step="0.1"
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500">
                  控制输出的随机性。0表示确定性输出，2表示最大随机性。留空使用模型默认值。
                </p>
              </div>
            </div>

            {/* 配置文件导入/导出 */}
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  accept=".json"
                  onChange={handleImportConfig}
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                >
                  <UploadIcon className="mr-2 h-4 w-4" />
                  导入配置
                </button>
                <button
                  onClick={handleExportConfig}
                  className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                >
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  导出配置
                </button>
              </div>
            </div>
          </div>

          <div className="mt-6 flex items-center justify-end space-x-4">
            {isSaved && (
              <span className="text-sm text-green-600">
                设置已保存
              </span>
            )}
            <Dialog.Close asChild>
              <button className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                取消
              </button>
            </Dialog.Close>
            <button
              onClick={handleSave}
              className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
            >
              保存
            </button>
          </div>

          <Dialog.Close asChild>
            <button className="absolute right-4 top-4 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500">
              <Cross2Icon />
            </button>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
