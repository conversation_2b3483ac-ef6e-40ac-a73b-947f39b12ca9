'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { extractCodeFromMessage, extractMultipleFilesFromMessage, generateDefaultFileName } from '../lib/code-extractor';
import ConversationPanel from './components/conversation-panel';
import ContentViewerPanel from './components/content-viewer-panel';
import { Conversation, GeneratedFile, FileStatus, ModelType, Task, TaskStatus, TaskExecutionPhase, FileOperation, FileOperationType, FileOperationStatus } from './types';
import AISettings from '@/components/ai-settings';
import { executeTask, generateSummary } from './task-functions';
import { promptTemplates } from './promptTemplates';
import { useAIStore } from '@/lib/ai-store';
import { useCookies } from 'next-client-cookies';
import { createContentStreamProcessor, ContentStreamProcessor } from '@/lib/streaming/content-stream-processor';

/**
 * 流式内容生成器客户端组件
 * 基于原有content-generator-client.tsx，增加流式输出支持
 */
export default function ContentGeneratorStream() {
  // 复用原有状态管理逻辑
  const [conversation, setConversation] = useState<Conversation>({
    id: `conv-${Date.now()}`,
    messages: [],
    createdAt: Date.now(),
    updatedAt: Date.now(),
  });

  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [contentType, setContentType] = useState<'html' | 'markdown'>('html');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [executionPhase, setExecutionPhase] = useState<TaskExecutionPhase>('planning');
  const [currentTaskIndex, setCurrentTaskIndex] = useState<number>(-1);
  const [fileOperations, setFileOperations] = useState<FileOperation[]>([]);

  // 流式处理相关状态
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [detectedFiles, setDetectedFiles] = useState<{type: 'html' | 'markdown', filename?: string}[]>([]);
  const [streamingProgress, setStreamingProgress] = useState<string>('正在思考...');
  const streamProcessorRef = useRef<ContentStreamProcessor | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 任务执行流式状态
  const [taskStreamingContent, setTaskStreamingContent] = useState<string>('');
  const [isTaskStreaming, setIsTaskStreaming] = useState(false);
  const [currentStreamingTaskId, setCurrentStreamingTaskId] = useState<string | null>(null);

  // AI配置
  const { provider, model } = useAIStore();
  const cookies = useCookies();

  // 布局相关状态
  const [leftPanelWidth, setLeftPanelWidth] = useState(600);
  const [isDragging, setIsDragging] = useState(false);
  const dragStartXRef = useRef(0);
  const leftPanelWidthRef = useRef(leftPanelWidth);

  // 固定布局，防止流式内容影响布局
  const containerStyle = {
    height: '100vh',
    overflow: 'hidden'
  };

  // 样式选项
  const [styleOptions, setStyleOptions] = useState({
    style: '简约现代',
    complexity: '中等',
    fileCount: 1,
  });

  // 上下文窗口配置
  const [contextOptions, setContextOptions] = useState({
    maxMessages: 0,
    keepSystemMessage: true,
  });

  // 生成唯一ID
  const generateUniqueId = useCallback((prefix: string) => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 添加自定义事件监听器，处理文件版本更新
  useEffect(() => {
    const handleVersionUpdate = (event: any) => {
      const { fileId, versions, versionIndex } = event.detail;

      console.log('[ContentGeneratorStream] 收到文件版本更新事件:', {
        fileId,
        versionsCount: versions.length,
        versionIndex,
        versions: versions.map((v: any) => v.taskDescription)
      });

      // 更新generatedFiles状态
      setGeneratedFiles(prev => {
        const updatedFiles = prev.map(file => {
          if (file.id === fileId) {
            return {
              ...file,
              versions,
              currentVersionIndex: versionIndex,
              isModified: true
            };
          }
          return file;
        });

        console.log('[ContentGeneratorStream] 更新文件版本数组:', {
          fileId,
          versionsCount: versions.length,
          totalFiles: updatedFiles.length
        });

        return updatedFiles;
      });
    };

    // 添加事件监听器
    document.addEventListener('file-version-updated', handleVersionUpdate);

    // 清理函数
    return () => {
      document.removeEventListener('file-version-updated', handleVersionUpdate);
    };
  }, []);

  // 文件操作管理
  const addFileOperation = useCallback((operation: Omit<FileOperation, 'id' | 'timestamp'>) => {
    const newOperation: FileOperation = {
      ...operation,
      id: generateUniqueId('op'),
      timestamp: Date.now(),
    };
    setFileOperations(prev => [...prev, newOperation]);
    return newOperation.id;
  }, [generateUniqueId]);

  const updateFileOperationStatus = useCallback((operationId: string, status: FileOperationStatus) => {
    setFileOperations(prev => prev.map(op =>
      op.id === operationId ? { ...op, status } : op
    ));
  }, []);

  // 创建文件版本
  const createFileVersion = useCallback((content: string, description: string, taskNumber?: number) => ({
    content,
    timestamp: Date.now(),
    taskDescription: description,
    taskNumber: taskNumber ?? currentTaskIndex + 1
  }), [currentTaskIndex]);

  // 初始化文件版本历史
  const initializeFileVersions = useCallback((content: string) => ({
    versions: [createFileVersion(content, '初始创建')],
    currentVersionIndex: 0
  }), [createFileVersion]);

  // 流式消息发送处理
  const handleSendMessageStream = async (content: string) => {
    if (!content.trim() || isStreaming) return;

    // 停止当前流（如果有）
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const userMessage = {
      id: `msg-${Date.now()}`,
      role: 'user' as const,
      content: content,
      timestamp: Date.now(),
      type: 'user' as 'user' | 'assistant' | 'task' | 'system'
    };

    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, userMessage],
    };

    setConversation(updatedConversation);
    setIsGenerating(true);
    setIsStreaming(true);
    setStreamingContent('');

    try {
      // 准备消息
      const messagesToSend = contextOptions.maxMessages > 0
        ? updatedConversation.messages.slice(-contextOptions.maxMessages)
        : updatedConversation.messages;

      const generatePromptWithFileInstructions = (userInput: string) => {
        const { fileInstructions, multiFileNote } = promptTemplates;
        const instructions = Object.values(fileInstructions).join('\n\n') + '\n' + multiFileNote;
        return `${userInput}\n\n${instructions}`;
      };

      const apiMessages = [
        ...messagesToSend.map(msg => ({ role: msg.role, content: msg.content })),
        { role: 'user' as const, content: generatePromptWithFileInstructions(content) },
      ];

      // 创建新的AbortController
      abortControllerRef.current = new AbortController();

      // 调用流式API
      const response = await fetch('/api/chat-stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: apiMessages, model }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      // 创建流处理器
      streamProcessorRef.current = createContentStreamProcessor({
        onContent: (delta: string, fullContent: string) => {
          setStreamingContent(fullContent);

          // 实时检测文件
          const detectedFileTypes = [];

          // 检测HTML文件
          const htmlMatches = fullContent.match(/```html\{filename=([^}]+)\}/g) ||
                            fullContent.match(/```html/g);
          if (htmlMatches) {
            const filenameMatch = fullContent.match(/```html\{filename=([^}]+)\}/);
            detectedFileTypes.push({
              type: 'html' as const,
              filename: filenameMatch ? filenameMatch[1] : 'index.html'
            });
          }

          // 检测Markdown文件
          const markdownMatches = fullContent.match(/```markdown\{filename=([^}]+)\}/g) ||
                                fullContent.match(/```md\{filename=([^}]+)\}/g) ||
                                fullContent.match(/```markdown/g);
          if (markdownMatches) {
            const filenameMatch = fullContent.match(/```(?:markdown|md)\{filename=([^}]+)\}/);
            detectedFileTypes.push({
              type: 'markdown' as const,
              filename: filenameMatch ? filenameMatch[1] : 'index.md'
            });
          }

          // 更新检测到的文件
          setDetectedFiles(detectedFileTypes);

          // 更新进度状态
          if (detectedFileTypes.length > 0) {
            const fileNames = detectedFileTypes.map(f => f.filename).join(', ');
            setStreamingProgress(`正在生成 ${fileNames}...`);
          } else {
            setStreamingProgress('正在思考...');
          }

          // 检测内容类型变化
          const hasHtmlCode = fullContent.includes('```html') ||
                           fullContent.includes('<!DOCTYPE html') ||
                           fullContent.includes('<html');
          const hasMarkdownCode = fullContent.includes('```markdown') ||
                               fullContent.includes('```md') ||
                               fullContent.includes('# ');

          if (hasHtmlCode && contentType !== 'html') {
            setContentType('html');
          } else if (hasMarkdownCode && contentType !== 'markdown') {
            setContentType('markdown');
          }
        },
        onFileDetected: (fileInfo) => {
          console.log('检测到文件:', fileInfo);
        },
        onTaskDetected: (tasks) => {
          console.log('检测到任务:', tasks);
        },
        onError: (error) => {
          console.error('流处理错误:', error);
          setIsStreaming(false);
          setIsGenerating(false);
          setDetectedFiles([]);
          setStreamingProgress('');
        },
        onFinish: (finalContent) => {
          console.log('流处理完成');

          // 添加AI回复到对话
          const messageId = `msg-${Date.now()}`;
          setConversation(prev => ({
            ...prev,
            messages: [
              ...prev.messages,
              {
                id: messageId,
                role: 'assistant' as const,
                content: finalContent,
                timestamp: Date.now(),
                type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
              }
            ]
          }));

          // 智能判断是否需要进行任务规划
          console.log('开始判断是否需要任务规划...');
          console.log('用户输入:', userMessage.content);
          console.log('AI回复长度:', finalContent.length);
          console.log('AI回复前100字符:', finalContent.substring(0, 100));

          const shouldExtractTasks = shouldPerformTaskPlanning(userMessage.content, finalContent);
          console.log('任务规划判断结果:', shouldExtractTasks);

          if (shouldExtractTasks) {
            console.log('开始提取任务...');
            // 处理任务提取
            const extractedTasks = extractTasksFromResponse(finalContent);
            console.log('提取到的任务数量:', extractedTasks.length);
            console.log('提取到的任务:', extractedTasks);

            if (extractedTasks.length > 0) {
              console.log('设置任务状态...');
              setTasks(extractedTasks);
              setExecutionPhase('executing');
              setCurrentTaskIndex(0);

              console.log('创建Todo.md文件...');
              // 创建Todo.md文件
              updateTodoFile(true);

              console.log('准备执行第一个任务...');
              // 自动开始执行第一个任务
              setTimeout(() => {
                executeNextTask(extractedTasks, 0);
              }, 1000);
            } else {
              console.log('没有提取到任务，处理文件提取...');
              // 处理文件提取
              const extractedFiles = extractMultipleFilesFromMessage(finalContent, messageId);
              if (extractedFiles.length > 0) {
                processExtractedFiles(extractedFiles);
              }
            }
          } else {
            // 不进行任务规划，直接处理文件提取
            const extractedFiles = extractMultipleFilesFromMessage(finalContent, messageId);
            if (extractedFiles.length > 0) {
              processExtractedFiles(extractedFiles);
            }
          }

          // 清理流式状态
          setStreamingContent('');
          setIsStreaming(false);
          setIsGenerating(false);
          setDetectedFiles([]);
          setStreamingProgress('');
        }
      });

      // 开始处理流
      await streamProcessorRef.current.processStream(response);

    } catch (error) {
      console.error('Error in stream processing:', error);

      if (error instanceof Error && error.name !== 'AbortError') {
        setConversation(prev => ({
          ...prev,
          messages: [
            ...prev.messages,
            {
              id: `msg-${Date.now()}`,
              role: 'assistant' as const,
              content: `抱歉，我暂时无法处理您的请求。当前使用的模型是 ${model}。请尝试重新发送消息或更换模型。`,
              timestamp: Date.now(),
              type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
            }
          ]
        }));
      }

      setStreamingContent('');
      setIsStreaming(false);
      setIsGenerating(false);
    }
  };

  // 停止流式处理
  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (streamProcessorRef.current) {
      streamProcessorRef.current.abort();
    }
    setIsStreaming(false);
    setIsGenerating(false);
    setStreamingContent('');
  }, []);

  // 渲染任务列表
  const renderTaskList = useCallback((tasks: Task[], level = 0): string => {
    return tasks.map(task => {
      const indent = '  '.repeat(level);
      const checked = task.status === 'completed' ? '[x]' : '[ ]';
      const cleanDescription = task.description.replace(/【任务\d+(?:\.\d+)*】/g, '').trim();

      let line = `${indent}- ${checked} ${cleanDescription}`;
      if (task.subtasks && Array.isArray(task.subtasks) && task.subtasks.length > 0) {
        line += '\n' + renderTaskList(task.subtasks, level + 1);
      }
      return line;
    }).join('\n');
  }, []);

  // 检查所有任务是否完成
  const areAllTasksCompleted = useCallback((tasks: Task[]): boolean => {
    return tasks.every(task => {
      if (task.status !== 'completed') return false;
      if (task.subtasks && task.subtasks.length > 0) {
        return areAllTasksCompleted(task.subtasks);
      }
      return true;
    });
  }, []);

  // 标记所有任务为完成
  const markAllCompleted = useCallback((tasks: Task[]): Task[] => {
    return tasks.map(task => ({
      ...task,
      status: 'completed' as TaskStatus,
      subtasks: task.subtasks ? markAllCompleted(task.subtasks) : undefined
    }));
  }, []);

  // 更新Todo文件
  const updateTodoFile = useCallback((createOperation = false) => {
    // 如果任务列表为空，不创建或更新Todo.md文件
    if (tasks.length === 0) {
      return;
    }

    const allCompleted = areAllTasksCompleted(tasks);
    let tasksForMd = [...tasks]; // 创建任务的副本

    if (allCompleted) {
      // 只为显示创建已完成的任务副本，不更新原始状态
      tasksForMd = markAllCompleted(tasks);
    }

    // 去重任务（基于任务编号）
    const deduplicateTasks = (ts: Task[]): Task[] => {
      const taskMap = new Map<number, Task>();

      const processTasks = (taskList: Task[]) => {
        taskList.forEach(task => {
          if (!taskMap.has(task.number) || taskMap.get(task.number)!.level > task.level) {
            taskMap.set(task.number, task);
          }
          if (task.subtasks && task.subtasks.length > 0) {
            processTasks(task.subtasks);
          }
        });
      };

      processTasks(ts);

      return Array.from(taskMap.values()).sort((a, b) => a.number - b.number);
    };

    const dedupedTasks = deduplicateTasks(tasksForMd);

    const todoContent = `## 任务清单\n\n${renderTaskList(dedupedTasks)}`;

    const todoFileIndex = generatedFiles.findIndex(file => file.name === 'Todo.md');

    let operationId: string | undefined;

    if (createOperation) {
      setFileOperations(prev => prev.filter(op => op.fileName !== 'Todo.md'));
      const operationType = todoFileIndex >= 0 ? 'update' : 'create';
      operationId = addFileOperation({
        type: operationType as FileOperationType,
        fileName: 'Todo.md',
        fileType: 'markdown',
        status: 'pending',
        taskNumber: undefined
      });
      updateFileOperationStatus(operationId, 'in-progress');
    }

    if (todoFileIndex >= 0) {
      const existingFile = generatedFiles[todoFileIndex];

      const lastVersion = existingFile.versions && existingFile.versions.length > 0
        ? existingFile.versions[existingFile.versions.length - 1]
        : { content: existingFile.content };

      if (lastVersion.content !== todoContent) {
        const newVersion = createFileVersion(todoContent, '更新任务状态');
        const versions = existingFile.versions || [{
          content: existingFile.content,
          timestamp: existingFile.timestamp,
          taskDescription: '初始版本'
        }];

        const updatedTodoFile: GeneratedFile = {
          ...existingFile,
          content: todoContent,
          timestamp: Date.now(),
          versions: [...versions, newVersion],
          currentVersionIndex: versions.length,
          isModified: true
        };

        setGeneratedFiles(prev => {
          const newFiles = [...prev];
          newFiles[todoFileIndex] = updatedTodoFile;
          return newFiles;
        });
      }
    } else {
      const todoFile: GeneratedFile = {
        id: generateUniqueId('file'),
        name: 'Todo.md',
        description: 'Markdown - 任务清单',
        content: todoContent,
        contentType: 'markdown',
        status: 'completed' as FileStatus,
        order: generatedFiles.length,
        viewMode: 'preview',
        timestamp: Date.now(),
        ...initializeFileVersions(todoContent),
        isModified: false
      };

      setGeneratedFiles(prev => [...prev, todoFile]);
    }

    if (allCompleted && operationId) {
      setFileOperations(prev => prev.map(op =>
        op.fileName === 'Todo.md' && op.status !== 'completed'
          ? { ...op, status: 'completed' as FileOperationStatus }
          : op
      ));
    }
  }, [tasks, generatedFiles, setFileOperations, addFileOperation, updateFileOperationStatus, generateUniqueId, renderTaskList, areAllTasksCompleted, markAllCompleted]);

  // 智能判断是否需要进行任务规划
  const shouldPerformTaskPlanning = (userInput: string, aiResponse: string): boolean => {
    // 如果已经有任务在执行中，不进行新的任务规划
    if (tasks.length > 0 && executionPhase === 'executing') {
      console.log('任务执行中，跳过任务规划');
      return false;
    }

    // 检查用户输入是否是对现有内容的反馈或修改意见
    const feedbackPatterns = [
      // 直接否定
      /^(任务\d+.*?)不对$/,
      /^(.*?)不对$/,
      /^不对/,

      // 修改意见
      /修改|改一下|调整|优化|更新|换个|重新/,
      /问题|错误|bug|有误|不好|不行|不符合/,
      /太.*?了|过于|应该|建议|希望|要求/,
      /颜色|样式|布局|字体|大小|位置|设计/,

      // 数字列表（可能是AI回复的总结，不应该被当作任务）
      /^\d+\.\s*.*?\n\d+\.\s*/m
    ];

    // 检查AI回复是否包含任务标记
    const hasTaskMarkers = /【任务\d+(?:\.\d+)*】/.test(aiResponse);

    // 检查用户输入是否是新的项目需求
    const projectKeywords = [
      '创建', '生成', '制作', '开发', '设计', '写个', '做个', '建立', '写一个',
      '网站', '页面', 'PPT', '演示', '文档', '应用', '系统', '项目',
      '帮我', '我想要', '我需要'
    ];

    // 特殊的项目类型关键词，即使输入很短也认为是项目需求
    const projectTypeKeywords = ['PPT', '网站', '页面', '文档', '应用', '系统'];

    const hasProjectKeyword = projectKeywords.some(keyword => userInput.includes(keyword));
    const hasProjectType = projectTypeKeywords.some(keyword => userInput.includes(keyword));

    // 增强的反馈检测：排除包含项目关键词的简短输入
    const isShortInput = userInput.length <= 10;
    const isShortFeedback = isShortInput && !hasProjectKeyword;

    const isFeedback = feedbackPatterns.some(pattern => pattern.test(userInput.trim())) || isShortFeedback;

    if (isFeedback) {
      console.log('检测到用户反馈或修改意见，跳过任务规划');
      return false;
    }

    // 如果包含项目类型关键词，或者包含项目关键词且长度合理，则认为是新项目
    const isNewProject = hasProjectKeyword && (hasProjectType || userInput.length > 5);

    // 检查是否是全新的对话（没有已完成的任务）
    const isNewConversation = tasks.length === 0;

    // 只有在以下情况下才进行任务规划：
    // 1. AI回复包含任务标记
    // 2. 用户输入看起来是新项目需求
    // 3. 不是用户反馈
    // 4. 是新对话或当前没有正在执行的任务
    const shouldPlan = hasTaskMarkers && isNewProject && !isFeedback &&
                      (isNewConversation || executionPhase !== 'executing');

    console.log('任务规划判断:', {
      hasTaskMarkers,
      hasProjectKeyword,
      hasProjectType,
      isNewProject,
      isFeedback,
      isNewConversation,
      executionPhase,
      shouldPlan,
      userInputLength: userInput.length,
      userInput: userInput.substring(0, 50) + '...'
    });

    return shouldPlan;
  };

  // 清理任务描述
  const cleanTaskDescription = (description: string): string => {
    return description
      .replace(/【任务\d+(?:\.\d+)*】/g, '')
      .replace(/^\s*[\d\.\-\*\+]\s*/, '')
      .replace(/^\s*Task\s*\d+\s*[:：]\s*/i, '')
      .replace(/^\s*步骤\s*\d+\s*[:：]\s*/i, '')
      .trim();
  };

  // 验证任务
  const validateTasks = (tasks: Task[]): Task[] => {
    return tasks.filter(task => {
      if (!task.description || task.description.trim().length === 0) {
        console.warn(`任务 ${task.number} 描述为空，已过滤`);
        return false;
      }
      if (task.description.length > 200) {
        console.warn(`任务 ${task.number} 描述过长，已截断`);
        task.description = task.description.substring(0, 200) + '...';
      }
      return true;
    });
  };

  // 提取任务的完整上下文信息
  const extractTaskContext = (response: string, taskNumber: string): string => {
    // 查找任务标记的位置
    const taskRegex = new RegExp(`【任务${taskNumber}】([^【]*?)(?=【任务\\d+】|$)`, 's');
    const match = taskRegex.exec(response);

    if (match) {
      // 提取任务标记后的所有内容，直到下一个任务标记
      let context = match[1].trim();

      // 清理格式，保留重要信息
      context = context
        .replace(/\n\s*\n/g, '\n') // 移除多余空行
        .replace(/^\s*-\s*/gm, '- ') // 规范化列表格式
        .trim();

      return context;
    }

    return '';
  };

  // 任务提取逻辑（增强版，包含完整上下文）
  const extractTasksFromResponse = (response: string): Task[] => {
    console.log('从模型响应中提取任务...');

    const taskMap: Record<string, Task> = {};
    const topLevelTasks: Task[] = [];
    let lastTaskAtLevel: Record<number, Task> = {};

    const taskProtocolRegex = /【任务(\d+(?:\.\d+)*)】([^\n]+)/g;
    let match;

    while ((match = taskProtocolRegex.exec(response)) !== null) {
      const taskNumberStr = match[1];
      let taskTitle = match[2].trim();

      const taskParts = taskNumberStr.split('.');
      const taskNumber = parseInt(taskParts[0]);
      const level = taskParts.length - 1;

      const taskIdentifier = taskNumberStr;

      // 提取完整的任务上下文
      const taskContext = extractTaskContext(response, taskNumberStr);

      // 组合任务描述：标题 + 上下文
      let fullTaskDescription = cleanTaskDescription(taskTitle);
      if (taskContext) {
        fullTaskDescription += '\n' + taskContext;
      }

      const task: Task = {
        id: generateUniqueId('task'),
        number: taskNumber,
        description: fullTaskDescription,
        status: 'pending',
        level,
        context: taskContext // 保存原始上下文
      };

      taskMap[taskIdentifier] = task;

      if (level === 0) {
        topLevelTasks.push(task);
      } else {
        const parentParts = taskParts.slice(0, -1);
        const parentIdentifier = parentParts.join('.');

        if (taskMap[parentIdentifier]) {
          task.parentId = taskMap[parentIdentifier].id;
          if (!taskMap[parentIdentifier].subtasks) {
            taskMap[parentIdentifier] = {
                ...taskMap[parentIdentifier],
                subtasks: []
              };
          }
          taskMap[parentIdentifier].subtasks!.push(task);
        } else {
          console.warn(`找不到任务 ${taskNumberStr} 的父任务 ${parentIdentifier}，将其作为顶级任务处理`);
          topLevelTasks.push(task);
        }
      }

      lastTaskAtLevel[level] = task;
    }

    // 如果没有找到任务，尝试备用方法
    if (topLevelTasks.length === 0) {
      console.log('未找到符合协议的任务标记，尝试使用备用提取方法...');

      const fallbackRegex1 = /(?:^|\n)(\s*)(\d+(?:\.\d+)*)[\.\u3001)\:\\uff1a]\s*([^\n]+)/g;
      const taskNumbers = new Set<number>();

      while ((match = fallbackRegex1.exec(response)) !== null) {
        const indentation = match[1] || '';
        const level = Math.floor(indentation.length / 2);
        const taskNumberStr = match[2];
        let taskDescription = match[3].trim();

        const taskParts = taskNumberStr.split('.');
        const taskNumber = parseInt(taskParts[0]);

        if (taskNumbers.has(taskNumber)) {
          console.log(`发现重复任务编号: ${taskNumber}，跳过`);
          continue;
        }

        taskNumbers.add(taskNumber);

        taskDescription = cleanTaskDescription(taskDescription);

        const task: Task = {
          id: generateUniqueId('task'),
          number: taskNumber,
          description: taskDescription,
          status: 'pending',
          level
        };

        taskMap[taskNumber.toString()] = task;
        topLevelTasks.push(task);
      }
    }

    topLevelTasks.sort((a, b) => a.number - b.number);

    const validatedTasks = validateTasks(topLevelTasks);

    console.log(`提取到 ${validatedTasks.length} 个顶级任务，总计 ${Object.keys(taskMap).length} 个任务（含子任务）`);

    return validatedTasks;
  };

  // 处理提取的文件 - 支持多文件和版本管理
  const processExtractedFiles = useCallback((extractedFiles: any[], taskDescription?: string) => {
    console.log('处理提取的文件:', extractedFiles.length);

    const updatedFiles: GeneratedFile[] = [];
    const newFiles: GeneratedFile[] = [];

    for (const [index, file] of extractedFiles.entries()) {
      // 改进文件名生成逻辑
      let fileName = file.filename;
      if (!fileName) {
        if (file.contentType === 'html') {
          fileName = extractedFiles.length === 1 ? 'index.html' : `page-${index + 1}.html`;
        } else {
          fileName = extractedFiles.length === 1 ? 'content.md' : `content-${index + 1}.md`;
        }
      }

      console.log(`处理文件 ${index + 1}:`, {
        fileName,
        contentType: file.contentType,
        contentLength: file.content.length,
        taskDescription
      });

      const existingFileIndex = generatedFiles.findIndex(
        f => f.name === fileName && f.contentType === file.contentType
      );

      if (existingFileIndex >= 0) {
        // 更新现有文件
        const existingFile = generatedFiles[existingFileIndex];

        const lastVersion = existingFile.versions && existingFile.versions.length > 0
          ? existingFile.versions[existingFile.versions.length - 1]
          : { content: existingFile.content };

        if (lastVersion.content !== file.content) {
          const versionDescription = taskDescription ||
            (currentTaskIndex >= 0 ? `任务${currentTaskIndex + 1}生成` : '流式生成的内容');

          const newVersion = createFileVersion(file.content, versionDescription, currentTaskIndex + 1);
          const versions = existingFile.versions || [{
            content: existingFile.content,
            timestamp: existingFile.timestamp,
            taskDescription: '初始版本'
          }];

          const updatedFile: GeneratedFile = {
            ...existingFile,
            content: file.content,
            timestamp: Date.now(),
            versions: [...versions, newVersion],
            currentVersionIndex: versions.length,
            isModified: true
          };

          updatedFiles.push(updatedFile);

          console.log(`更新文件 ${fileName}，新增版本 ${versions.length + 1}`);
        } else {
          console.log(`文件 ${fileName} 内容未变化，跳过更新`);
        }
      } else {
        // 创建新文件
        const versionDescription = taskDescription ||
          (currentTaskIndex >= 0 ? `任务${currentTaskIndex + 1}生成` : '初始创建');

        const newFile: GeneratedFile = {
          id: generateUniqueId('file'),
          name: fileName,
          description: `${file.contentType === 'html' ? 'HTML' : 'Markdown'} - ${fileName}`,
          content: file.content,
          contentType: file.contentType,
          status: 'completed' as FileStatus,
          order: generatedFiles.length + newFiles.length,
          viewMode: 'preview',
          timestamp: Date.now(),
          ...initializeFileVersions(file.content),
          isModified: false
        };

        // 更新初始版本的描述
        if (newFile.versions && newFile.versions.length > 0) {
          newFile.versions[0].taskDescription = versionDescription;
          newFile.versions[0].taskNumber = currentTaskIndex + 1;
        }

        newFiles.push(newFile);

        console.log(`创建新文件 ${fileName}`);
      }
    }

    // 批量更新文件列表
    setGeneratedFiles(prev => {
      // 移除被更新的文件
      const filteredFiles = prev.filter(file =>
        !updatedFiles.some(updatedFile =>
          updatedFile.name === file.name && updatedFile.contentType === file.contentType
        )
      );

      // 合并所有文件
      const allFiles = [...filteredFiles, ...updatedFiles, ...newFiles];

      console.log(`文件列表已更新: 总计${allFiles.length}个文件，新增${newFiles.length}个，更新${updatedFiles.length}个`);

      return allFiles;
    });
  }, [generatedFiles, currentTaskIndex, generateUniqueId, createFileVersion, initializeFileVersions]);

  // 版本管理函数
  const handleViewModeChange = (fileId: string, viewMode: 'code' | 'preview' | 'split') => {
    setGeneratedFiles(prev => prev.map(file =>
      file.id === fileId ? { ...file, viewMode } : file
    ));
  };

  /**
   * 处理文件版本切换
   * 1. 找到目标文件
   * 2. 合并同名文件的所有版本历史
   * 3. 按时间排序并切换到指定版本
   * 4. 更新文件内容和版本信息
   */
  const handleVersionChange = useCallback((fileId: string, versionIndex: number) => {
    console.log('[handleVersionChange] 请求切换版本:', { fileId, versionIndex });

    // 1. 找到目标文件，并获取其基本信息
    const targetFile = generatedFiles.find(f => f.id === fileId);
    if (!targetFile) {
      console.error('[handleVersionChange] 未找到目标文件:', fileId);
      return;
    }

    // 2. 合并同名文件的版本历史
    const sameNameFiles = generatedFiles.filter(f =>
      f.name === targetFile.name && f.contentType === targetFile.contentType
    );

    // 3. 合并所有版本并按时间排序
    // 定义版本类型
    type FileVersion = NonNullable<GeneratedFile['versions']>[number];

    // 合并所有版本
    const allVersions = sameNameFiles.reduce<FileVersion[]>((acc, file) => {
      if (file.versions) {
        acc.push(...file.versions);
      } else if (file.content) {
        // 如果文件没有版本历史，将当前内容作为一个版本
        acc.push({
          content: file.content,
          timestamp: file.timestamp,
          taskDescription: '初始版本'
        });
      }
      return acc;
    }, []).sort((a, b) => a.timestamp - b.timestamp);

    // 4. 验证版本索引的有效性
    if (!allVersions.length) {
      console.error('[handleVersionChange] 文件没有任何可用版本:', {
        fileId,
        name: targetFile.name,
        contentType: targetFile.contentType
      });
      return;
    }

    if (versionIndex < 0 || versionIndex >= allVersions.length) {
      console.error('[handleVersionChange] 无效的版本索引:', {
        versionIndex,
        totalVersions: allVersions.length
      });
      return;
    }

    // 5. 获取目标版本
    const targetVersion = allVersions[versionIndex];

    console.log('[handleVersionChange] 切换版本详情:', {
      fileId,
      fileName: targetFile.name,
      fromVersion: targetFile.currentVersionIndex,
      toVersion: versionIndex,
      totalVersions: allVersions.length,
      versionTimestamp: targetVersion.timestamp,
      versionDescription: targetVersion.taskDescription
    });

    // 6. 更新文件内容和版本信息
    setGeneratedFiles(prev => {
      const updatedFiles = prev.map(file => {
        if (file.id === fileId) {
          return {
            ...file,
            content: targetVersion.content,
            versions: allVersions,
            currentVersionIndex: versionIndex,
            isModified: true
          };
        }
        return file;
      });

      console.log('[handleVersionChange] 更新完成:', {
        updatedFileId: fileId,
        newVersionIndex: versionIndex,
        totalFiles: updatedFiles.length
      });

      return updatedFiles;
    });
  }, [generatedFiles]);

  // 执行下一个任务
  const executeNextTask = useCallback(async (taskList: Task[], taskIndex: number) => {
    if (taskIndex >= taskList.length) {
      // 所有任务执行完成
      setExecutionPhase('completed');
      return;
    }

    const currentTask = taskList[taskIndex];
    if (!currentTask) return;

    console.log(`开始执行任务 ${currentTask.number}: ${currentTask.description}`);

    // 更新任务状态
    setTasks(prev => prev.map(t =>
      t.id === currentTask.id ? { ...t, status: 'in-progress' as TaskStatus } : t
    ));

    // 获取原始用户需求作为上下文
    const originalUserRequest = conversation.messages.find(msg => msg.role === 'user')?.content || '';

    // 创建任务执行消息 - 明确指示执行具体任务
    const taskExecutionPrompt = `现在请执行具体任务，直接生成文件内容：

【原始需求】${originalUserRequest}

【当前执行任务】
任务编号：${currentTask.number}
任务描述：${currentTask.description}

【执行要求】
1. 这是一个具体的执行任务，不是规划任务
2. 直接生成完整的文件内容，不要重新规划或拆分
3. 如果任务要求创建HTML文件，请生成完整的HTML代码
4. 如果任务要求创建其他文件，请生成相应的完整内容
5. 使用正确的代码块格式包装文件内容，例如：
   \`\`\`html{filename=slide-1-cover.html}
   <!-- 完整的HTML内容 -->
   \`\`\`
6. 确保生成的内容符合原始需求和当前任务描述

请立即开始执行这个具体任务，生成所需的文件：`;

    const taskMessage = {
      id: generateUniqueId('msg'),
      role: 'user' as const,
      content: taskExecutionPrompt,
      timestamp: Date.now(),
      type: 'task' as const,
      taskId: currentTask.id
    };

    // 创建用户友好的任务显示消息
    const userFriendlyTaskMessage = {
      id: generateUniqueId('msg'),
      role: 'user' as const,
      content: `执行任务${currentTask.number}：${currentTask.description}`,
      timestamp: Date.now(),
      type: 'task' as const,
      taskId: currentTask.id
    };

    // 添加用户友好的任务消息到对话（用于显示）
    setConversation(prev => ({
      ...prev,
      messages: [...prev.messages, userFriendlyTaskMessage]
    }));

    try {
      setIsGenerating(true);

      // 调用流式API执行任务
      const response = await fetch('/api/chat-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...conversation.messages.slice(0, -1), taskMessage].map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          model,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to execute task');
      }

      // 使用流式处理器处理响应（支持续写）
      let fullContent = '';

      // 设置任务流式状态
      setIsTaskStreaming(true);
      setCurrentStreamingTaskId(currentTask.id);
      setTaskStreamingContent('');

      const taskStreamProcessor = createContentStreamProcessor({
        onContent: (delta: string, content: string) => {
          fullContent = content;
          // 实时更新任务流式内容
          setTaskStreamingContent(content);

          // 同时更新右侧预览区域的流式内容
          setStreamingContent(content);
          setIsStreaming(true);

          // 实时检测文件 - 与普通对话一样的检测逻辑
          const currentDetectedFiles = extractMultipleFilesFromMessage(content, 'temp-task-id');
          if (currentDetectedFiles.length > 0) {
            const fileTypes = currentDetectedFiles.map(file => ({
              type: file.contentType as 'html' | 'markdown',
              filename: file.filename
            }));
            setDetectedFiles(fileTypes);

            // 更新流式进度显示
            const fileNames = fileTypes.map(f => f.filename || `${f.type}文件`).join('、');
            setStreamingProgress(`正在生成 ${fileNames}...`);
          } else {
            setStreamingProgress(`正在执行任务${currentTask.number}...`);
          }
        },
        onFinish: (finalContent: string) => {
          fullContent = finalContent;
          console.log(`任务执行完成，最终内容长度: ${finalContent.length}`);
          // 清理任务流式状态
          setIsTaskStreaming(false);
          setCurrentStreamingTaskId(null);
          setTaskStreamingContent('');

          // 清理右侧流式状态
          setStreamingContent('');
          setIsStreaming(false);

          // 清理文件检测状态
          setDetectedFiles([]);
          setStreamingProgress('');
        },
        onError: (error: Error) => {
          console.error('任务执行流处理错误:', error);
          // 清理任务流式状态
          setIsTaskStreaming(false);
          setCurrentStreamingTaskId(null);
          setTaskStreamingContent('');

          // 清理右侧流式状态
          setStreamingContent('');
          setIsStreaming(false);

          // 清理文件检测状态
          setDetectedFiles([]);
          setStreamingProgress('');
        }
      });

      // 处理流式响应
      await taskStreamProcessor.processStream(response);

      // 添加AI回复到对话
      const messageId = `msg-${Date.now()}`;
      setConversation(prev => ({
        ...prev,
        messages: [
          ...prev.messages,
          {
            id: messageId,
            role: 'assistant' as const,
            content: fullContent,
            timestamp: Date.now(),
            type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
          }
        ]
      }));

      // 提取文件
      const extractedFiles = extractMultipleFilesFromMessage(fullContent, messageId);
      if (extractedFiles.length > 0) {
        const taskDescription = `任务${currentTaskIndex + 1}: ${currentTask.description}`;
        processExtractedFiles(extractedFiles, taskDescription);
      }

      // 更新任务状态为完成
      setTasks(prev => prev.map(t =>
        t.id === currentTask.id ? { ...t, status: 'completed' as TaskStatus, result: fullContent } : t
      ));

      setIsGenerating(false);
      setCurrentTaskIndex(taskIndex + 1);

      // 继续执行下一个任务
      // setTimeout(() => {
        executeNextTask(taskList, taskIndex + 1);
      // }, 1000);

    } catch (error) {
      console.error('Error executing task:', error);

      // 更新任务状态为失败
      setTasks(prev => prev.map(t =>
        t.id === currentTask.id ? { ...t, status: 'pending' as TaskStatus } : t
      ));

      setIsGenerating(false);
    }
  }, [conversation, model, generateUniqueId, processExtractedFiles, currentTaskIndex]);

  // 同步任务状态到Todo文件
  useEffect(() => {
    // 只有当任务列表不为空时才更新Todo.md
    if (tasks.length > 0) {
      updateTodoFile(false); // 不创建新操作，仅同步内容
    } else {
      // 如果任务列表为空且Todo.md存在，则从生成的文件列表中移除它
      const todoFileIndex = generatedFiles.findIndex(file => file.name === 'Todo.md');
      if (todoFileIndex >= 0) {
        setGeneratedFiles(prev => prev.filter(file => file.name !== 'Todo.md'));
      }
    }
  }, [tasks, updateTodoFile, generatedFiles]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (streamProcessorRef.current) {
        streamProcessorRef.current.abort();
      }
    };
  }, []);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white" style={containerStyle}>
      <header className="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
        <h1 className="text-xl font-bold flex items-center gap-2">
          <svg className="w-6 h-6 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12a3 3 0 1 0 6 0 3 3 0 0 0-6 0z" />
          </svg>
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 font-extrabold">
            面向过程文件的AIGC原生内容生成智能体 (流式版)
          </span>
        </h1>
        <div className="flex items-center gap-2">
          {isStreaming && (
            <button
              onClick={stopStreaming}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm"
            >
              停止生成
            </button>
          )}
          <AISettings />
        </div>
      </header>

      <main className="flex flex-1 overflow-hidden min-h-0">
        <div className="flex flex-1 overflow-hidden min-h-0">
          {/* 左侧对话面板 */}
          <div
            className="flex-shrink-0 overflow-hidden bg-gray-800 border-r border-gray-700 min-h-0"
            style={{
              width: `${leftPanelWidth}px`,
              minWidth: `${leftPanelWidth}px`,
              maxWidth: `${leftPanelWidth}px`
            }}
          >
            <ConversationPanel
              conversation={conversation}
              onSendMessage={handleSendMessageStream}
              contentType={contentType}
              setContentType={setContentType}
              styleOptions={styleOptions}
              setStyleOptions={setStyleOptions}
              contextOptions={contextOptions}
              setContextOptions={setContextOptions}
              onGenerateContent={() => {}} // 流式版本不需要单独的生成按钮
              isGenerating={isGenerating}
              tasks={tasks}
              executionPhase={executionPhase}
              fileOperations={fileOperations}
              streamingContent={streamingContent}
              isStreaming={isStreaming}
              detectedFiles={detectedFiles}
              streamingProgress={streamingProgress}
              taskStreamingContent={taskStreamingContent}
              isTaskStreaming={isTaskStreaming}
              currentStreamingTaskId={currentStreamingTaskId}
            />
          </div>

          {/* 可拖拽分隔线 */}
          <div className="w-4 flex items-center justify-center cursor-col-resize bg-gray-800 hover:bg-indigo-800 flex-shrink-0">
            <div className="h-10 w-[2px] bg-cyan-400 rounded-full glow-effect"></div>
          </div>

          {/* 右侧内容预览面板 */}
          <div className="flex-1 overflow-hidden bg-gray-850 min-h-0 min-w-0">
            <ContentViewerPanel
              files={generatedFiles}
              isGenerating={isGenerating}
              onViewModeChange={handleViewModeChange}
              onVersionChange={handleVersionChange}
              streamingContent={streamingContent}
              isStreaming={isStreaming}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
